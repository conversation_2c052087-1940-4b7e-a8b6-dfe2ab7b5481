[project]
name = 'clarity-bounty-marketplace'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.bid-collateral]
path = 'contracts/bid-collateral.clar'
clarity_version = 3
epoch = 3.1

[contracts.bounty-manager]
path = 'contracts/bounty-manager.clar'
clarity_version = 3
epoch = 3.1

[contracts.dispute-resolution]
path = 'contracts/dispute-resolution.clar'
clarity_version = 3
epoch = 3.1

[contracts.escrow]
path = 'contracts/escrow.clar'
clarity_version = 3
epoch = 3.1

[contracts.governance]
path = 'contracts/governance.clar'
clarity_version = 3
epoch = 3.1

[contracts.reputation]
path = 'contracts/reputation.clar'
clarity_version = 3
epoch = 3.1

[contracts.token-wrapper]
path = 'contracts/token-wrapper.clar'
clarity_version = 3
epoch = 3.1

[contracts.utils]
path = 'contracts/utils.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
