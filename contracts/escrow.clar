;; Escrow contract for bounty marketplace

;; Error constants
(define-constant ERR_NOT_FOUND (err u102))
(define-constant ERR_INVALID_AMOUNT (err u103))
(define-constant ERR_UNAUTHORIZED (err u100))
(define-constant ERR_ALREADY_APPROVED (err u114))
(define-constant ERR_NOT_APPROVED (err u115))

;; Map to track balances for each user and bounty
(define-map balances {
  user: principal,
  bounty-id: uint
} uint)

;; Map to track bounty completion approvals
(define-map approvals {
  bounty-id: uint
} {
  approved: bool,
  approver: principal,
  worker: principal,
  amount: uint
})

;; Lock funds in escrow for a specific bounty
(define-public (lock-funds (bounty-id uint) (amount uint))
  (begin
    ;; Transfer STX from user to contract
    (try! (stx-transfer? amount tx-sender (as-contract tx-sender)))

    ;; Update balance in escrow
    (let ((balance (default-to u0 (map-get? balances {user: tx-sender, bounty-id: bounty-id}))))
      (map-set balances {user: tx-sender, bounty-id: bounty-id} (+ balance amount)))

    (ok true)))

;; Approve completion and release funds to worker
(define-public (approve-completion (bounty-id uint) (worker principal))
  (let ((balance (unwrap! (map-get? balances {user: tx-sender, bounty-id: bounty-id}) ERR_NOT_FOUND)))
    (asserts! (> balance u0) ERR_INVALID_AMOUNT)
    (asserts! (is-none (map-get? approvals {bounty-id: bounty-id})) ERR_ALREADY_APPROVED)

    ;; Record approval
    (map-set approvals {bounty-id: bounty-id} {
      approved: true,
      approver: tx-sender,
      worker: worker,
      amount: balance
    })

    ;; Transfer funds to worker
    (as-contract (try! (stx-transfer? balance tx-sender worker)))

    ;; Clear balance
    (map-set balances {user: tx-sender, bounty-id: bounty-id} u0)

    (ok true)))

;; Refund funds back to the original depositor (in case of dispute resolution)
(define-public (refund (bounty-id uint) (to principal))
  (let ((balance (unwrap! (map-get? balances {user: tx-sender, bounty-id: bounty-id}) ERR_NOT_FOUND)))
    (asserts! (> balance u0) ERR_INVALID_AMOUNT)

    ;; Transfer funds back to specified recipient
    (as-contract (try! (stx-transfer? balance tx-sender to)))

    ;; Clear balance
    (map-set balances {user: tx-sender, bounty-id: bounty-id} u0)

    (ok true)))

;; Emergency release (only for authorized parties like dispute resolution)
(define-public (emergency-release (bounty-id uint) (from principal) (to principal) (amount uint))
  (let ((balance (unwrap! (map-get? balances {user: from, bounty-id: bounty-id}) ERR_NOT_FOUND)))
    (asserts! (>= balance amount) ERR_INVALID_AMOUNT)
    ;; TODO: Add authorization check for dispute resolution contract

    ;; Transfer specified amount
    (as-contract (try! (stx-transfer? amount tx-sender to)))

    ;; Update balance
    (map-set balances {user: from, bounty-id: bounty-id} (- balance amount))

    (ok true)))

;; Read-only functions
(define-read-only (get-balance (user principal) (bounty-id uint))
  (default-to u0 (map-get? balances {user: user, bounty-id: bounty-id})))

(define-read-only (get-approval (bounty-id uint))
  (map-get? approvals {bounty-id: bounty-id}))

(define-read-only (is-approved (bounty-id uint))
  (match (map-get? approvals {bounty-id: bounty-id})
    approval (get approved approval)
    false))