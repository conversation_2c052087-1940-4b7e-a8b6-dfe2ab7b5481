(define-map balances {
  user: principal,
  bounty-id: uint
} uint)

(define-public (lock-funds (bounty-id uint) (amount uint))
  (let ((balance (default-to u0 (map-get? balances {user: tx-sender, bounty-id})))
    (map-set balances {user: tx-sender, bounty-id} (+ balance amount))
    (ok true)))

(define-public (release-funds (bounty-id uint) (to principal) (amount uint))
  (let ((balance (unwrap! (map-get? balances {user: tx-sender, bounty-id}) ERR_NOT_FOUND)))
    (asserts! (>= balance amount) ERR_INVALID_AMOUNT)
    (map-set balances {user: tx-sender, bounty-id} (- balance amount))
    (ok true)))