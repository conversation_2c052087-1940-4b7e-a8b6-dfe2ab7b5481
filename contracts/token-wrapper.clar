;; Token wrapper contract for STX
(define-fungible-token stx-wrapper)

;; Error constants
(define-constant ERR_INSUFFICIENT_BALANCE (err u104))
(define-constant ERR_TRANSFER_FAILED (err u105))

;; Wrap STX into stx-wrapper tokens
(define-public (wrap-stx (amount uint))
  (begin
    ;; Transfer STX from user to contract
    (try! (stx-transfer? amount tx-sender (as-contract tx-sender)))
    ;; Mint equivalent stx-wrapper tokens to user
    (ft-mint? stx-wrapper amount tx-sender)))

;; Unwrap stx-wrapper tokens back to STX
(define-public (unwrap-stx (amount uint))
  (begin
    ;; Burn stx-wrapper tokens from user
    (try! (ft-burn? stx-wrapper amount tx-sender))
    ;; Transfer STX from contract to user
    (as-contract (stx-transfer? amount tx-sender (as-contract tx-sender)))))

;; Get balance of stx-wrapper tokens for a user
(define-read-only (get-balance (user principal))
  (ft-get-balance stx-wrapper user))

;; Get total supply of stx-wrapper tokens
(define-read-only (get-total-supply)
  (ft-get-supply stx-wrapper))