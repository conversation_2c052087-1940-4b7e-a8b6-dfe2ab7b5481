;; Bid and collateral management contract
(define-constant ERR_BID_NOT_FOUND (err u110))
(define-constant ERR_BID_ALREADY_EXISTS (err u111))
(define-constant ERR_INSUFFICIENT_COLLATERAL (err u112))
(define-constant ERR_UNAUTHORIZED (err u100))
(define-constant ERR_INVALID_STATUS (err u113))

;; Map to store bid information
(define-map bids {
  bounty-id: uint,
  bidder: principal
} {
  amount: uint,
  collateral: uint,
  status: (string-ascii 10)  ;; "ACTIVE"|"WITHDRAWN"|"ACCEPTED"|"SLASHED"
})

;; Map to track collateral balances
(define-map collateral-balances principal uint)

;; Minimum collateral percentage (10% of bid amount)
(define-data-var min-collateral-rate uint u10)

;; Place a bid with collateral
(define-public (place-bid (bounty-id uint) (amount uint) (collateral uint))
  (let ((min-collateral (/ (* amount (var-get min-collateral-rate)) u100)))
    (asserts! (>= collateral min-collateral) ERR_INSUFFICIENT_COLLATERAL)
    (asserts! (is-none (map-get? bids {bounty-id: bounty-id, bidder: tx-sender})) ERR_BID_ALREADY_EXISTS)

    ;; Transfer collateral from bidder to contract
    (try! (stx-transfer? collateral tx-sender (as-contract tx-sender)))

    ;; Store bid information
    (map-set bids {bounty-id: bounty-id, bidder: tx-sender} {
      amount: amount,
      collateral: collateral,
      status: "ACTIVE"
    })

    ;; Update collateral balance
    (let ((current-balance (default-to u0 (map-get? collateral-balances tx-sender))))
      (map-set collateral-balances tx-sender (+ current-balance collateral)))

    (ok true)))

;; Withdraw a bid (only if not accepted)
(define-public (withdraw-bid (bounty-id uint))
  (let ((bid (unwrap! (map-get? bids {bounty-id: bounty-id, bidder: tx-sender}) ERR_BID_NOT_FOUND)))
    (asserts! (is-eq (get status bid) "ACTIVE") ERR_INVALID_STATUS)

    ;; Update bid status
    (map-set bids {bounty-id: bounty-id, bidder: tx-sender} (merge bid {status: "WITHDRAWN"}))

    ;; Return collateral to bidder
    (let ((collateral (get collateral bid)))
      (as-contract (try! (stx-transfer? collateral tx-sender (as-contract tx-sender))))

      ;; Update collateral balance
      (let ((current-balance (default-to u0 (map-get? collateral-balances tx-sender))))
        (map-set collateral-balances tx-sender (- current-balance collateral))))

    (ok true)))

;; Accept a bid (called by bounty creator)
(define-public (accept-bid (bounty-id uint) (bidder principal))
  (let ((bid (unwrap! (map-get? bids {bounty-id: bounty-id, bidder: bidder}) ERR_BID_NOT_FOUND)))
    (asserts! (is-eq (get status bid) "ACTIVE") ERR_INVALID_STATUS)

    ;; Update bid status
    (map-set bids {bounty-id: bounty-id, bidder: bidder} (merge bid {status: "ACCEPTED"}))

    (ok true)))

;; Slash collateral (called in case of dispute resolution against bidder)
(define-public (slash-collateral (bounty-id uint) (bidder principal))
  (let ((bid (unwrap! (map-get? bids {bounty-id: bounty-id, bidder: bidder}) ERR_BID_NOT_FOUND)))
    (asserts! (is-eq (get status bid) "ACCEPTED") ERR_INVALID_STATUS)

    ;; Update bid status
    (map-set bids {bounty-id: bounty-id, bidder: bidder} (merge bid {status: "SLASHED"}))

    ;; Collateral remains in contract (slashed)
    (let ((collateral (get collateral bid))
          (current-balance (default-to u0 (map-get? collateral-balances bidder))))
      (map-set collateral-balances bidder (- current-balance collateral)))

    (ok true)))

;; Release collateral back to bidder (after successful completion)
(define-public (release-collateral (bounty-id uint) (bidder principal))
  (let ((bid (unwrap! (map-get? bids {bounty-id: bounty-id, bidder: bidder}) ERR_BID_NOT_FOUND)))
    (asserts! (is-eq (get status bid) "ACCEPTED") ERR_INVALID_STATUS)

    ;; Return collateral to bidder
    (let ((collateral (get collateral bid)))
      (as-contract (try! (stx-transfer? collateral tx-sender bidder)))

      ;; Update collateral balance
      (let ((current-balance (default-to u0 (map-get? collateral-balances bidder))))
        (map-set collateral-balances bidder (- current-balance collateral))))

    (ok true)))

;; Read-only functions
(define-read-only (get-bid (bounty-id uint) (bidder principal))
  (map-get? bids {bounty-id: bounty-id, bidder: bidder}))

(define-read-only (get-collateral-balance (user principal))
  (default-to u0 (map-get? collateral-balances user)))

(define-read-only (get-min-collateral-rate)
  (var-get min-collateral-rate))