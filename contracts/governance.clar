;; Governance contract for bounty marketplace settings
(define-constant ERR_UNAUTHORIZED (err u100))

;; Settings data variables
(define-data-var fee-rate uint u3)                    ;; Fee percentage (e.g., 3 for 3%)
(define-data-var dispute-window uint u144)            ;; Blocks for dispute period (~24 hours)
(define-data-var arbitrator principal 'SP3FBR2AGK5H9QBDH3EEN6DF8EK8JY7RX8QJ5SVTE)

;; Update fee rate (only arbitrator can call)
(define-public (update-fee-rate (new-rate uint))
  (begin
    (asserts! (is-eq tx-sender (var-get arbitrator)) ERR_UNAUTHORIZED)
    (var-set fee-rate new-rate)
    (ok true)))

;; Update dispute window (only arbitrator can call)
(define-public (update-dispute-window (new-window uint))
  (begin
    (asserts! (is-eq tx-sender (var-get arbitrator)) ERR_UNAUTHORIZED)
    (var-set dispute-window new-window)
    (ok true)))

;; Update arbitrator (only current arbitrator can call)
(define-public (update-arbitrator (new-arbitrator principal))
  (begin
    (asserts! (is-eq tx-sender (var-get arbitrator)) ERR_UNAUTHORIZED)
    (var-set arbitrator new-arbitrator)
    (ok true)))

;; Read-only functions to get current settings
(define-read-only (get-fee-rate)
  (var-get fee-rate))

(define-read-only (get-dispute-window)
  (var-get dispute-window))

(define-read-only (get-arbitrator)
  (var-get arbitrator))