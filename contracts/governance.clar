(define-data-var settings {
  fee-rate: uint,         ;; Fee percentage (e.g., 3 for 3%)
  dispute-window: uint,    ;; Blocks for dispute period
  arbitrator: principal
} {
  fee-rate: u3,
  dispute-window: u144,  ;; ~24 hours
  arbitrator: 'SP3FBR2AGK5H9QBDH3EEN6DF8EK8JY7RX8QJ5SVTE
})

(define-public (update-setting (key (string-ascii 20)) (value principal))
  (begin
    (asserts! (is-eq tx-sender (get arbitrator)) ERR_UNAUTHORIZED)
    (var-set settings (map-set (var-get settings) key value))
    (ok true)))