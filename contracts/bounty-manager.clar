(define-non-fungible-token bounty-nft uint)

(define-map bounties uint {
  creator: principal,
  amount: uint,
  status: (string-ascii 20)  ;; "OPEN"|"ASSIGNED"|"COMPLETED"|"DISPUTED"
})

(define-public (create-bounty (amount uint))
  (begin
    (nft-mint? bounty-nft amount tx-sender)
    (map-set bounties amount {
      creator: tx-sender,
      amount: amount,
      status: "OPEN"
    })
    (ok true)))