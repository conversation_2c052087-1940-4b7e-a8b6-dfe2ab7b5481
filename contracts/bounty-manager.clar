;; Bounty management contract
(define-non-fungible-token bounty-nft uint)

;; Error constants
(define-constant ERR_BOUNTY_EXISTS (err u107))
(define-constant ERR_BOUNTY_NOT_FOUND (err u108))
(define-constant ERR_INVALID_STATUS (err u109))
(define-constant ERR_UNAUTHORIZED (err u100))
(define-constant ERR_BOUNTY_EXPIRED (err u122))
(define-constant ERR_BOUNTY_NOT_OPEN (err u123))

;; Map to store bounty information
(define-map bounties uint {
  creator: principal,
  amount: uint,
  title: (string-utf8 100),
  description: (string-utf8 500),
  status: (string-ascii 20),  ;; "OPEN"|"ASSIGNED"|"COMPLETED"|"DISPUTED"|"EXPIRED"
  assigned-to: (optional principal),
  created-at: uint,
  expires-at: uint,
  completion-deadline: uint
})

;; Counter for bounty IDs
(define-data-var next-bounty-id uint u1)

;; Default bounty duration (in blocks)
(define-data-var default-bounty-duration uint u1008)  ;; ~1 week

;; Create a new bounty
(define-public (create-bounty (amount uint) (title (string-utf8 100)) (description (string-utf8 500)) (duration uint))
  (let ((bounty-id (var-get next-bounty-id))
        (current-block stacks-block-height)
        (expiry-block (+ current-block duration)))

    (try! (nft-mint? bounty-nft bounty-id tx-sender))

    (map-set bounties bounty-id {
      creator: tx-sender,
      amount: amount,
      title: title,
      description: description,
      status: "OPEN",
      assigned-to: none,
      created-at: current-block,
      expires-at: expiry-block,
      completion-deadline: (+ expiry-block u144)  ;; Extra time for completion
    })

    (var-set next-bounty-id (+ bounty-id u1))
    (ok bounty-id)))

;; Assign bounty to a worker
(define-public (assign-bounty (bounty-id uint) (worker principal))
  (let ((bounty (unwrap! (map-get? bounties bounty-id) ERR_BOUNTY_NOT_FOUND)))
    (asserts! (is-eq tx-sender (get creator bounty)) ERR_UNAUTHORIZED)
    (asserts! (is-eq (get status bounty) "OPEN") ERR_BOUNTY_NOT_OPEN)
    (asserts! (< stacks-block-height (get expires-at bounty)) ERR_BOUNTY_EXPIRED)

    (map-set bounties bounty-id (merge bounty {
      status: "ASSIGNED",
      assigned-to: (some worker)
    }))

    (ok true)))

;; Mark bounty as completed
(define-public (complete-bounty (bounty-id uint))
  (let ((bounty (unwrap! (map-get? bounties bounty-id) ERR_BOUNTY_NOT_FOUND)))
    (asserts! (is-eq (some tx-sender) (get assigned-to bounty)) ERR_UNAUTHORIZED)
    (asserts! (is-eq (get status bounty) "ASSIGNED") ERR_INVALID_STATUS)
    (asserts! (< stacks-block-height (get completion-deadline bounty)) ERR_BOUNTY_EXPIRED)

    (map-set bounties bounty-id (merge bounty {status: "COMPLETED"}))

    (ok true)))

;; Expire a bounty (can be called by anyone after expiry)
(define-public (expire-bounty (bounty-id uint))
  (let ((bounty (unwrap! (map-get? bounties bounty-id) ERR_BOUNTY_NOT_FOUND)))
    (asserts! (>= stacks-block-height (get expires-at bounty)) ERR_BOUNTY_EXPIRED)
    (asserts! (not (is-eq (get status bounty) "COMPLETED")) ERR_INVALID_STATUS)

    (map-set bounties bounty-id (merge bounty {status: "EXPIRED"}))

    (ok true)))

;; Update bounty status (admin function)
(define-public (update-bounty-status (bounty-id uint) (new-status (string-ascii 20)))
  (let ((bounty (unwrap! (map-get? bounties bounty-id) ERR_BOUNTY_NOT_FOUND)))
    (asserts! (is-eq tx-sender (get creator bounty)) ERR_UNAUTHORIZED)
    (map-set bounties bounty-id (merge bounty {status: new-status}))
    (ok true)))

;; Cancel a bounty (only creator, only if not assigned)
(define-public (cancel-bounty (bounty-id uint))
  (let ((bounty (unwrap! (map-get? bounties bounty-id) ERR_BOUNTY_NOT_FOUND)))
    (asserts! (is-eq tx-sender (get creator bounty)) ERR_UNAUTHORIZED)
    (asserts! (is-eq (get status bounty) "OPEN") ERR_BOUNTY_NOT_OPEN)

    (map-set bounties bounty-id (merge bounty {status: "CANCELLED"}))

    (ok true)))

;; Read-only functions
(define-read-only (get-bounty (bounty-id uint))
  (map-get? bounties bounty-id))

(define-read-only (get-next-bounty-id)
  (var-get next-bounty-id))

(define-read-only (is-bounty-expired (bounty-id uint))
  (match (map-get? bounties bounty-id)
    bounty (>= stacks-block-height (get expires-at bounty))
    true))

(define-read-only (get-bounty-status (bounty-id uint))
  (match (map-get? bounties bounty-id)
    bounty (get status bounty)
    "NOT_FOUND"))

(define-read-only (is-bounty-assigned-to (bounty-id uint) (user principal))
  (match (map-get? bounties bounty-id)
    bounty (is-eq (some user) (get assigned-to bounty))
    false))