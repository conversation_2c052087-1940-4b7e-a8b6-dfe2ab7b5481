;; Dispute resolution contract with jury system

;; Error constants
(define-constant ERR_DISPUTE_NOT_FOUND (err u116))
(define-constant ERR_DISPUTE_ALREADY_EXISTS (err u117))
(define-constant ERR_DISPUTE_RESOLVED (err u118))
(define-constant ERR_NOT_JURY_MEMBER (err u119))
(define-constant ERR_ALREADY_VOTED (err u120))
(define-constant ERR_INSUFFICIENT_STAKE (err u121))
(define-constant ERR_UNAUTHORIZED (err u100))

;; Map to store dispute information
(define-map disputes uint {
  initiator: principal,
  defendant: principal,
  reason: (string-utf8 100),
  resolved: bool,
  resolution: (string-ascii 20),  ;; "FAVOR_INITIATOR"|"FAVOR_DEFENDANT"|"PENDING"
  jury-size: uint,
  votes-for: uint,
  votes-against: uint
})

;; Map to track jury members and their stakes
(define-map jury-members principal {
  stake: uint,
  reputation: uint,
  active: bool
})

;; Map to track votes for each dispute
(define-map dispute-votes {
  dispute-id: uint,
  voter: principal
} {
  vote: bool,  ;; true = favor initiator, false = favor defendant
  weight: uint
})

;; Minimum stake required to be a jury member
(define-data-var min-jury-stake uint u1000)

;; Register as a jury member
(define-public (register-jury (stake uint))
  (begin
    (asserts! (>= stake (var-get min-jury-stake)) ERR_INSUFFICIENT_STAKE)

    ;; Transfer stake to contract
    (try! (stx-transfer? stake tx-sender (as-contract tx-sender)))

    ;; Register jury member
    (map-set jury-members tx-sender {
      stake: stake,
      reputation: u100,  ;; Starting reputation
      active: true
    })

    (ok true)))

;; Initiate a dispute
(define-public (initiate-dispute (bounty-id uint) (defendant principal) (reason (string-utf8 100)))
  (begin
    (asserts! (is-none (map-get? disputes bounty-id)) ERR_DISPUTE_ALREADY_EXISTS)

    (map-set disputes bounty-id {
      initiator: tx-sender,
      defendant: defendant,
      reason: reason,
      resolved: false,
      resolution: "PENDING",
      jury-size: u3,  ;; Default jury size
      votes-for: u0,
      votes-against: u0
    })

    (ok true)))

;; Vote on a dispute (jury members only)
(define-public (vote-dispute (dispute-id uint) (vote-for-initiator bool))
  (let ((dispute (unwrap! (map-get? disputes dispute-id) ERR_DISPUTE_NOT_FOUND))
        (jury-member (unwrap! (map-get? jury-members tx-sender) ERR_NOT_JURY_MEMBER)))

    (asserts! (not (get resolved dispute)) ERR_DISPUTE_RESOLVED)
    (asserts! (get active jury-member) ERR_NOT_JURY_MEMBER)
    (asserts! (is-none (map-get? dispute-votes {dispute-id: dispute-id, voter: tx-sender})) ERR_ALREADY_VOTED)

    ;; Calculate vote weight based on reputation and stake
    (let ((vote-weight (+ (get reputation jury-member) (/ (get stake jury-member) u100))))

      ;; Record vote
      (map-set dispute-votes {dispute-id: dispute-id, voter: tx-sender} {
        vote: vote-for-initiator,
        weight: vote-weight
      })

      ;; Update vote counts
      (if vote-for-initiator
          (map-set disputes dispute-id (merge dispute {votes-for: (+ (get votes-for dispute) vote-weight)}))
          (map-set disputes dispute-id (merge dispute {votes-against: (+ (get votes-against dispute) vote-weight)})))

      ;; Check if we have enough votes to resolve
      (let ((updated-dispute (unwrap! (map-get? disputes dispute-id) ERR_DISPUTE_NOT_FOUND)))
        (if (>= (+ (get votes-for updated-dispute) (get votes-against updated-dispute)) (* (get jury-size updated-dispute) u100))
            (resolve-dispute-internal dispute-id)
            (ok true))))))

;; Internal function to resolve dispute based on votes
(define-private (resolve-dispute-internal (dispute-id uint))
  (let ((dispute (unwrap! (map-get? disputes dispute-id) ERR_DISPUTE_NOT_FOUND)))
    (let ((resolution (if (> (get votes-for dispute) (get votes-against dispute))
                          "FAVOR_INITIATOR"
                          "FAVOR_DEFENDANT")))

      (map-set disputes dispute-id (merge dispute {
        resolved: true,
        resolution: resolution
      }))

      (ok true))))

;; Manually resolve dispute (admin function)
(define-public (admin-resolve-dispute (dispute-id uint) (resolution (string-ascii 20)))
  (let ((dispute (unwrap! (map-get? disputes dispute-id) ERR_DISPUTE_NOT_FOUND)))
    ;; TODO: Add admin authorization check
    (asserts! (not (get resolved dispute)) ERR_DISPUTE_RESOLVED)

    (map-set disputes dispute-id (merge dispute {
      resolved: true,
      resolution: resolution
    }))

    (ok true)))

;; Read-only functions
(define-read-only (get-dispute (dispute-id uint))
  (map-get? disputes dispute-id))

(define-read-only (get-jury-member (member principal))
  (map-get? jury-members member))

(define-read-only (get-vote (dispute-id uint) (voter principal))
  (map-get? dispute-votes {dispute-id: dispute-id, voter: voter}))

(define-read-only (is-dispute-resolved (dispute-id uint))
  (match (map-get? disputes dispute-id)
    dispute (get resolved dispute)
    false))