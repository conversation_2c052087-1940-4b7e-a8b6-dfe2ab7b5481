;; Reputation system for bounty marketplace
(define-map scores principal uint)

;; Error constants
(define-constant ERR_INVALID_DELTA (err u106))

;; Update reputation score for a user
(define-public (update-reputation (user principal) (delta int))
  (let ((current (default-to u0 (map-get? scores user))))
    (if (>= delta 0)
        ;; Positive delta: add to current score
        (map-set scores user (+ current (to-uint delta)))
        ;; Negative delta: subtract from current score (minimum 0)
        (if (>= current (to-uint (- delta)))
            (map-set scores user (- current (to-uint (- delta))))
            (map-set scores user u0)))
    (ok true)))

;; Increase reputation (convenience function for positive changes)
(define-public (increase-reputation (user principal) (amount uint))
  (update-reputation user (to-int amount)))

;; Decrease reputation (convenience function for negative changes)
(define-public (decrease-reputation (user principal) (amount uint))
  (update-reputation user (- (to-int amount))))

;; Get reputation score for a user
(define-read-only (get-reputation (user principal))
  (default-to u0 (map-get? scores user)))