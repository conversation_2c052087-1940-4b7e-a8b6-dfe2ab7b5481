# Decentralized Bounty & Task Marketplace

A fully modularized on‑chain freelance platform built in Clarity for the Stacks blockchain. This project enables requesters to post bounties, workers to bid by staking collateral, on‑chain escrow, and a reputation‑weighted, decentralized jury system for dispute resolution. Backed by seven core smart contracts, a comprehensive test suite, and full Clarinet integration, this repository ensures seamless `clarinet check` and `clarinet test` workflows.

---

## Table of Contents

1. [Features](#features)
2. [Architecture](#architecture)
3. [Folder Structure](#folder-structure)
4. [Prerequisites](#prerequisites)
5. [Installation & Setup](#installation--setup)
6. [Usage](#usage)

   * [Deploying Contracts](#deploying-contracts)
   * [CLI Commands](#cli-commands)
   * [Example Flows](#example-flows)
7. [Contracts Overview](#contracts-overview)
8. [Testing](#testing)
9. [Contribution Guidelines](#contribution-guidelines)
10. [License](#license)

---

## Features

* **Bounty Management**: Requesters can create, edit, and expire bounties with STX or fungible tokens.
* **Worker Bidding & Collateral**: Workers stake collateral to bid, discouraging spam and ensuring commitment.
* **On‑Chain Escrow**: Funds are securely locked until task completion and explicit approval.
* **Decentralized Dispute Resolution**: Reputation‑weighted jury drawn from staked community members resolves conflicts.
* **Reputation System**: On‑chain scoring drives bidding priority and jury influence.
* **Governance Module**: DAO‑style proposals adjust platform fees, jury thresholds, and other parameters.
* **Modular Architecture**: Seven clarity modules, each responsible for a distinct domain.
* **Comprehensive Tests**: `clarinet test` covers happy paths, edge cases, and dispute flows.

---

## Architecture

Below is a high‑level Mermaid diagram illustrating core modules and flow:

```mermaid
flowchart LR
  A[Bounty Manager] --> B[Escrow]
  B --> C[Approval]
  C --> D[Fund Release]
  B --> E[Dispute Resolution]
  E --> D
  F[Bid & Collateral] --> A
  G[Reputation] --> F
  G --> E
  H[Governance] --> A
  H --> E
  I[Token Wrapper] --> A
  I --> F
```

---

## Folder Structure

```
clarity-bounty-marketplace/
├─ contracts/
│  ├─ bounty-manager.clar         # Create/Edit/Delete/Expire bounties
│  ├─ bid-collateral.clar         # Worker bids & collateral logic
│  ├─ escrow.clar                 # On‑chain escrow locking & release
│  ├─ dispute-resolution.clar     # Jury registration, voting, slashing
│  ├─ reputation.clar             # On‑chain reputation ledger
│  ├─ governance.clar             # DAO proposals & parameter updates
│  ├─ token-wrapper.clar          # Uniform token handling (STX & FT)
│  └─ utils.clar                  # Shared types, constants, error codes
├─ tests/
│  ├─ test-bounty-manager.ts
│  ├─ test-bid-collateral.ts
│  ├─ test-escrow.ts
│  ├─ test-dispute-resolution.ts
│  ├─ test-reputation.ts
│  ├─ test-governance.ts
│  └─ test-token-wrapper.ts
├─ Clarinet.toml                  # Clarinet configuration
├─ README.md                      # Project documentation (this file)
└─ PULL_REQUEST.md                # Detailed PR description and test plan
```

---

## Prerequisites

* **Node.js** >= 16.x
* **Yarn** or **npm**
* **Clarinet** CLI >= 1.0.0
* **Stacks Testnet / Local Devnet** (default: local)

---

## Installation & Setup

1. **Clone the repo**

   ```bash
   git clone https://github.com/your-org/clarity-bounty-marketplace.git
   cd clarity-bounty-marketplace
   ```

2. **Install dependencies**

   ```bash
   yarn install
   # or
   npm install
   ```

3. **Configure Clarinet**

   Verify `Clarinet.toml` points to your desired network (local/hyperserver/testnet).

---

## Usage

### Deploying Contracts

To deploy all contracts in a local devnet:

```bash
clarinet localnet start --reset
clarinet deploy
```

### CLI Commands

* **Check compilation**

  ```bash
  clarinet check
  ```
* **Run tests**

  ```bash
  clarinet test
  ```
* **Generate coverage report**

  ```bash
  clarinet coverage
  ```

### Example Flows

1. **Post a bounty**

   ```bash
   clarinet call-contract contract bounty-manager post-bounty '(u1000 "Fix bug in UI" 100)'
   ```
2. **Place a bid**

   ```bash
   clarinet call-contract contract bid-collateral place-bid '(1 u50)'
   ```
3. **Complete task & approve**

   ```bash
   clarinet call-contract contract escrow approve-completion '(1)'
   ```
4. **Initiate dispute**

   ```bash
   clarinet call-contract contract dispute-resolution raise-dispute '(1)'
   ```

---

## Contracts Overview

### `bounty-manager.clar`

* **Responsibilities:** Posting, editing, expiring bounties
* **Key Functions:** `post-bounty`, `update-bounty`, `expire-bounty`

### `bid-collateral.clar`

* **Responsibilities:** Worker bidding and collateral management
* **Key Functions:** `place-bid`, `withdraw-bid`, `slash-collateral`

### `escrow.clar`

* **Responsibilities:** Locking requester funds and releasing on approval
* **Key Functions:** `lock-funds`, `approve-completion`, `refund`

### `dispute-resolution.clar`

* **Responsibilities:** Jury staking, selection, voting, dispute outcome
* **Key Functions:** `register-jury`, `select-jury`, `vote`, `resolve-dispute`

### `reputation.clar`

* **Responsibilities:** Tracking on‑chain reputation for requesters, workers, jurors
* **Key Functions:** `add-reputation`, `subtract-reputation`, `get-reputation`

### `governance.clar`

* **Responsibilities:** Parameter proposals and voting
* **Key Functions:** `propose-parameter-change`, `vote-proposal`, `execute-proposal`

### `token-wrapper.clar`

* **Responsibilities:** Uniform interface for STX & fungible tokens
* **Key Functions:** `wrap-stx`, `unwrap-stx`, `transfer-ft`

### `utils.clar`

* **Responsibilities:** Shared types, constants, error codes, internal helpers

---

## Testing

The `tests/` directory contains TypeScript test files for each module. Key coverage areas:

* **Happy Path**: Posting bounties, successful bids, completed tasks
* **Edge Cases**: Expired bounties, insufficient collateral, re-entrancy attempts
* **Dispute Flow**: Jury registration, voting tie-breakers, slashing collateral

Run all tests via:

```bash
clarinet test
```

---

## Contribution Guidelines

1. **Fork** the repo and create a feature branch.
2. **Write tests** for any new functionality.
3. **Ensure** `clarinet check` and `clarinet test` pass locally.
4. **Open a PR** against `main` with a descriptive title and reference relevant issues.
5. **Follow** the style conventions in `utils.clar` and project lint rules.

---

## License

This project is licensed under the [MIT License](LICENSE). Feel free to use, modify, and distribute as you see fit.
